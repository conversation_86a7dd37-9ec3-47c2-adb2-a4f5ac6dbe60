#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色条管理器
统一管理压力和速度颜色条的创建、更新和显示
"""

import vtk
from typing import Dict, Optional, Tuple
from utils.error_handler import handle_errors, ErrorContext


class ColorBarManager:
    """颜色条管理器 - 统一管理所有颜色条"""
    
    def __init__(self, renderer: vtk.vtkRenderer):
        self.renderer = renderer
        self.active_color_bars: Dict[str, vtk.vtkScalarBarActor] = {}
        
        # 颜色条位置配置
        self.positions = {
            'pressure': (0.05, 0.1),  # 左侧
            'velocity': (0.85, 0.1),  # 右侧
            'temperature': (0.45, 0.1),  # 中间
        }
    
    @handle_errors(default_return=False, log_prefix="颜色条管理器")
    def update_color_bars(self, visibility_state: Dict[str, bool], 
                         global_ranges: Dict[str, Tuple[float, float]],
                         color_maps: Dict[str, vtk.vtkLookupTable]) -> bool:
        """
        根据可见性状态更新所有颜色条
        
        Args:
            visibility_state: 各类型可视化的可见性状态
            global_ranges: 各类型数据的全局范围
            color_maps: 各类型的颜色映射表
        
        Returns:
            bool: 是否更新成功
        """
        # 清除现有颜色条
        self._clear_all_color_bars()
        
        color_bars_added = 0
        
        # 添加压力颜色条
        if visibility_state.get('pressure', False) and 'pressure' in global_ranges:
            success = self._add_pressure_color_bar(
                global_ranges['pressure'], 
                color_maps.get('pressure')
            )
            if success:
                color_bars_added += 1
        
        # 添加速度颜色条
        if (visibility_state.get('streamlines', False) or 
            visibility_state.get('vectors', False)) and 'velocity' in global_ranges:
            success = self._add_velocity_color_bar(
                global_ranges['velocity'],
                color_maps.get('velocity')
            )
            if success:
                color_bars_added += 1
        
        # 添加温度颜色条（如果需要）
        # if visibility_state.get('temperature', False) and 'temperature' in global_ranges:
        #     success = self._add_temperature_color_bar(
        #         global_ranges['temperature'],
        #         color_maps.get('temperature')
        #     )
        #     if success:
        #         color_bars_added += 1
        
        print(f"颜色条更新完成: 添加了 {color_bars_added} 个颜色条")
        return True
    
    @handle_errors(default_return=False, log_prefix="压力颜色条")
    def _add_pressure_color_bar(self, pressure_range: Tuple[float, float], 
                               lut: Optional[vtk.vtkLookupTable] = None) -> bool:
        """添加压力颜色条"""
        if not lut:
            lut = self._create_rainbow_colormap()
        
        lut.SetRange(pressure_range)
        
        scalar_bar = self._create_scalar_bar(
            lut, 
            "Pressure [Pa]",
            position=self.positions['pressure']
        )
        
        if scalar_bar:
            self.active_color_bars['pressure'] = scalar_bar
            self.renderer.AddActor2D(scalar_bar)
            print(f"✓ 压力颜色条已添加: {pressure_range[0]:.2f} ~ {pressure_range[1]:.2f} Pa")
            return True
        
        return False
    
    @handle_errors(default_return=False, log_prefix="速度颜色条")
    def _add_velocity_color_bar(self, velocity_range: Tuple[float, float],
                               lut: Optional[vtk.vtkLookupTable] = None) -> bool:
        """添加速度颜色条"""
        if not lut:
            lut = self._create_rainbow_colormap()
        
        lut.SetRange(velocity_range)
        
        scalar_bar = self._create_scalar_bar(
            lut,
            "Velocity [m/s]",
            position=self.positions['velocity']
        )
        
        if scalar_bar:
            self.active_color_bars['velocity'] = scalar_bar
            self.renderer.AddActor2D(scalar_bar)
            print(f"✓ 速度颜色条已添加: {velocity_range[0]:.3f} ~ {velocity_range[1]:.3f} m/s")
            return True
        
        return False
    

    
    def _clear_all_color_bars(self):
        """清除所有颜色条"""
        with ErrorContext("清除颜色条"):
            for color_bar in self.active_color_bars.values():
                if color_bar:
                    self.renderer.RemoveActor2D(color_bar)
            
            self.active_color_bars.clear()
            print(f"已清除所有颜色条")
    
    def _create_scalar_bar(self, lut: vtk.vtkLookupTable, title: str,
                          position: Tuple[float, float] = (0.85, 0.1)) -> Optional[vtk.vtkScalarBarActor]:
        """创建标量条"""
        try:
            scalar_bar = vtk.vtkScalarBarActor()
            scalar_bar.SetLookupTable(lut)
            scalar_bar.SetTitle(title)
            scalar_bar.SetNumberOfLabels(5)
            
            # 设置位置和大小
            scalar_bar.SetPosition(position[0], position[1])
            scalar_bar.SetWidth(0.08)
            scalar_bar.SetHeight(0.8)
            
            # 设置文本属性
            title_prop = scalar_bar.GetTitleTextProperty()
            title_prop.SetFontSize(12)
            title_prop.SetColor(1.0, 1.0, 1.0)
            
            label_prop = scalar_bar.GetLabelTextProperty()
            label_prop.SetFontSize(10)
            label_prop.SetColor(1.0, 1.0, 1.0)
            
            return scalar_bar
        except Exception as e:
            print(f"创建标量条失败: {e}")
            return None
    
    def _create_rainbow_colormap(self) -> vtk.vtkLookupTable:
        """创建彩虹颜色映射"""
        lut = vtk.vtkLookupTable()
        lut.SetNumberOfColors(256)
        lut.SetHueRange(0.667, 0.0)  # 蓝到红
        lut.SetSaturationRange(1.0, 1.0)
        lut.SetValueRange(1.0, 1.0)
        lut.Build()
        return lut
