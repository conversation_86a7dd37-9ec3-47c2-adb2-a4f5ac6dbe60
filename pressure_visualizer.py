#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压力可视化器
负责创建压力分布和压力轮廓的可视化
"""

import vtk
import numpy as np
from typing import Optional, Tuple
from vtk_renderer import VTKRenderer


class PressureVisualizer(VTKRenderer):
    """压力可视化器"""
    
    def __init__(self):
        super().__init__()
        
    def create_pressure_surface(self, ugrid: vtk.vtkUnstructuredGrid) -> Optional[vtk.vtkActor]:
        """
        创建压力分布面渲染可视化
        
        Args:
            ugrid: VTK非结构化网格
            
        Returns:
            vtk.vtkActor: 压力分布actor或None
        """
        try:
            # 检查是否有标量数据
            if ugrid.GetPointData().GetScalars() is None:
                print("警告: 没有找到标量数据，使用点云可视化")
                return self._create_point_cloud_visualization(ugrid)
                
            num_points = ugrid.GetNumberOfPoints()
            print(f"创建压力分布面渲染... ({num_points} 个点)")
            
            # 对于大数据集，先进行采样以避免内存问题
            if num_points > 50000:
                print(f"数据点过多 ({num_points})，进行采样以提高性能...")
                ugrid = self.sample_ugrid_for_performance(ugrid, 50000)
                print(f"采样后点数: {ugrid.GetNumberOfPoints()}")
                
            # 根据数据量选择合适的策略
            if num_points < 10000:
                # 小数据集：尝试精确的面渲染
                surface_strategies = [
                    self._create_surface_with_delaunay2d,
                    self._create_surface_with_delaunay3d,
                    self._create_surface_with_point_gaussian
                ]
            else:
                # 大数据集：优先使用高效策略
                surface_strategies = [
                    self._create_surface_with_point_gaussian,
                    self._create_surface_with_delaunay2d
                ]
                
            for i, strategy in enumerate(surface_strategies):
                try:
                    print(f"尝试面渲染策略 {i+1}...")
                    surface = strategy(ugrid)
                    if surface is not None:
                        print(f"策略 {i+1} 成功创建面渲染")
                        return surface
                except Exception as e:
                    print(f"策略 {i+1} 失败: {e}")
                    continue
                    
            # 如果所有策略都失败，回退到点云
            print("所有面渲染策略失败，使用点云可视化")
            return self._create_point_cloud_visualization(ugrid)
            
        except Exception as e:
            print(f"创建压力可视化失败: {e}")
            return self._create_point_cloud_visualization(ugrid)

    def create_pressure_surface_with_range(self, ugrid: vtk.vtkUnstructuredGrid,
                                         global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """
        创建压力分布面渲染可视化（使用指定的全局颜色范围）

        Args:
            ugrid: VTK非结构化网格
            global_scalar_range: 全局压力范围 (min, max)

        Returns:
            vtk.vtkActor: 压力分布actor或None
        """
        try:
            # 检查是否有标量数据
            if ugrid.GetPointData().GetScalars() is None:
                print("警告: 没有找到标量数据，使用点云可视化")
                return self._create_point_cloud_visualization(ugrid)

            num_points = ugrid.GetNumberOfPoints()
            print(f"创建压力分布面渲染... ({num_points} 个点)")

            # 对于大数据集，先进行采样以避免内存问题
            if num_points > 50000:
                print(f"数据点过多 ({num_points})，进行采样以提高性能...")
                ugrid = self.sample_ugrid_for_performance(ugrid, 50000)
                print(f"采样后点数: {ugrid.GetNumberOfPoints()}")

            # 根据数据量选择合适的策略
            if num_points < 10000:
                # 小数据集：尝试精确的面渲染
                surface_strategies = [
                    lambda u: self._create_surface_with_delaunay2d_range(u, global_scalar_range),
                    lambda u: self._create_surface_with_delaunay3d_range(u, global_scalar_range),
                    lambda u: self._create_surface_with_point_gaussian_range(u, global_scalar_range)
                ]
            else:
                # 大数据集：优先使用高效策略
                surface_strategies = [
                    lambda u: self._create_surface_with_point_gaussian_range(u, global_scalar_range),
                    lambda u: self._create_surface_with_delaunay2d_range(u, global_scalar_range)
                ]

            for i, strategy in enumerate(surface_strategies):
                try:
                    print(f"尝试面渲染策略 {i+1}...")
                    surface = strategy(ugrid)
                    if surface is not None:
                        print(f"策略 {i+1} 成功创建面渲染")
                        return surface
                except Exception as e:
                    print(f"策略 {i+1} 失败: {e}")
                    continue

            # 如果所有策略都失败，回退到点云
            print("所有面渲染策略失败，使用点云可视化")
            return self._create_point_cloud_visualization(ugrid)

        except Exception as e:
            print(f"创建压力可视化失败: {e}")
            return self._create_point_cloud_visualization(ugrid)
            
    def create_pressure_contour(self, ugrid: vtk.vtkUnstructuredGrid, 
                              contour_count: int = 15, line_width: int = 2) -> Optional[vtk.vtkActor]:
        """
        创建壁面压力轮廓可视化
        
        Args:
            ugrid: VTK非结构化网格
            contour_count: 轮廓线数量
            line_width: 轮廓线宽度
            
        Returns:
            vtk.vtkActor: 压力轮廓actor或None
        """
        try:
            # 检查是否有标量数据
            if ugrid.GetPointData().GetScalars() is None:
                print("警告: 没有找到标量数据，无法创建压力轮廓")
                return None
                
            num_points = ugrid.GetNumberOfPoints()
            print(f"创建壁面压力轮廓... ({num_points} 个点)")
            
            # 获取压力数据范围
            scalar_range = ugrid.GetPointData().GetScalars().GetRange()
            min_pressure, max_pressure = scalar_range
            print(f"压力范围: {min_pressure:.2f} ~ {max_pressure:.2f} Pa")
            
            # 如果压力范围太小，无法创建有意义的轮廓
            if abs(max_pressure - min_pressure) < 1e-6:
                print("压力范围太小，无法创建轮廓")
                return None
                
            # 首先提取表面
            surface_filter = vtk.vtkDataSetSurfaceFilter()
            surface_filter.SetInputData(ugrid)
            surface_filter.Update()
            
            surface = surface_filter.GetOutput()
            if surface.GetNumberOfCells() == 0:
                print("无法提取表面")
                return None
                
            # 创建轮廓线
            contour_filter = vtk.vtkContourFilter()
            contour_filter.SetInputData(surface)
            
            # 设置轮廓值
            for i in range(contour_count):
                value = min_pressure + (max_pressure - min_pressure) * i / (contour_count - 1)
                contour_filter.SetValue(i, value)
                
            contour_filter.Update()
            
            contour_output = contour_filter.GetOutput()
            if contour_output.GetNumberOfCells() == 0:
                print("未生成任何轮廓线")
                return None
                
            print(f"生成了 {contour_output.GetNumberOfCells()} 个轮廓线段")
            
            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(contour_filter.GetOutputPort())
            mapper.SetScalarModeToUsePointData()
            mapper.SetScalarRange(scalar_range)
            
            # 设置彩虹色颜色映射
            lut = self.create_rainbow_colormap()
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)
            
            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            # 设置轮廓线外观
            actor.GetProperty().SetLineWidth(line_width)
            actor.GetProperty().SetRepresentationToWireframe()
            
            return actor
            
        except Exception as e:
            print(f"创建壁面压力轮廓失败: {e}")
            return None

    def create_pressure_contour_with_range(self, ugrid: vtk.vtkUnstructuredGrid,
                                         contour_count: int = 15, line_width: int = 2,
                                         global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """
        创建壁面压力轮廓可视化（使用指定的全局颜色范围）

        Args:
            ugrid: VTK非结构化网格
            contour_count: 轮廓线数量
            line_width: 轮廓线宽度
            global_scalar_range: 全局压力范围 (min, max)

        Returns:
            vtk.vtkActor: 压力轮廓actor或None
        """
        try:
            # 检查是否有标量数据
            if ugrid.GetPointData().GetScalars() is None:
                print("警告: 没有找到标量数据，无法创建压力轮廓")
                return None

            num_points = ugrid.GetNumberOfPoints()
            print(f"创建壁面压力轮廓... ({num_points} 个点)")

            # 使用全局范围或局部范围
            if global_scalar_range:
                min_pressure, max_pressure = global_scalar_range
                print(f"使用全局压力范围: {min_pressure:.2f} ~ {max_pressure:.2f} Pa")
            else:
                # 获取局部压力数据范围
                scalar_range = ugrid.GetPointData().GetScalars().GetRange()
                min_pressure, max_pressure = scalar_range
                print(f"使用局部压力范围: {min_pressure:.2f} ~ {max_pressure:.2f} Pa")

            # 如果压力范围太小，无法创建有意义的轮廓
            if abs(max_pressure - min_pressure) < 1e-6:
                print("压力范围太小，无法创建轮廓")
                return None

            # 首先提取表面
            surface_filter = vtk.vtkDataSetSurfaceFilter()
            surface_filter.SetInputData(ugrid)
            surface_filter.Update()

            surface = surface_filter.GetOutput()
            if surface.GetNumberOfCells() == 0:
                print("无法提取表面")
                return None

            # 创建轮廓线
            contour_filter = vtk.vtkContourFilter()
            contour_filter.SetInputData(surface)

            # 设置轮廓值（使用全局范围）
            for i in range(contour_count):
                value = min_pressure + (max_pressure - min_pressure) * i / (contour_count - 1)
                contour_filter.SetValue(i, value)

            contour_filter.Update()

            contour_output = contour_filter.GetOutput()
            if contour_output.GetNumberOfCells() == 0:
                print("未生成任何轮廓线")
                return None

            print(f"生成了 {contour_output.GetNumberOfCells()} 个轮廓线段")

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(contour_filter.GetOutputPort())
            mapper.SetScalarModeToUsePointData()
            mapper.SetScalarRange(min_pressure, max_pressure)  # 使用全局范围

            # 设置彩虹色颜色映射
            lut = self.create_rainbow_colormap()
            lut.SetRange(min_pressure, max_pressure)  # 使用全局范围
            mapper.SetLookupTable(lut)

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            # 设置轮廓线外观
            actor.GetProperty().SetLineWidth(line_width)
            actor.GetProperty().SetRepresentationToWireframe()

            return actor

        except Exception as e:
            print(f"创建壁面压力轮廓失败: {e}")
            return None
            
    def _create_surface_with_delaunay2d(self, ugrid: vtk.vtkUnstructuredGrid) -> Optional[vtk.vtkActor]:
        """使用2D Delaunay三角化创建表面"""
        try:
            # 将点转换为PolyData
            points_to_poly = vtk.vtkVertexGlyphFilter()
            points_to_poly.SetInputData(ugrid)
            points_to_poly.Update()
            
            # 检查点数
            poly_data = points_to_poly.GetOutput()
            if poly_data.GetNumberOfPoints() < 3:
                print("点数不足，无法进行三角化")
                return None
                
            # 使用简化的2D Delaunay三角化
            delaunay2d = vtk.vtkDelaunay2D()
            delaunay2d.SetInputData(poly_data)
            delaunay2d.SetProjectionPlaneMode(vtk.VTK_BEST_FITTING_PLANE)
            delaunay2d.SetAlpha(0.0)  # 不使用alpha形状
            delaunay2d.SetTolerance(0.001)
            
            # 设置边界条件以避免过度三角化
            bounds = ugrid.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
            delaunay2d.SetOffset(domain_size * 0.01)
            
            delaunay2d.Update()
            
            # 检查结果
            result = delaunay2d.GetOutput()
            if result.GetNumberOfCells() == 0:
                print("Delaunay2D未生成任何三角形")
                return None
                
            print(f"Delaunay2D生成了 {result.GetNumberOfCells()} 个三角形")
            
            # 创建映射器和演员
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(delaunay2d.GetOutputPort())
            mapper.SetScalarModeToUsePointData()
            
            scalar_range = ugrid.GetPointData().GetScalars().GetRange()
            mapper.SetScalarRange(scalar_range)
            
            # 设置彩虹色颜色映射（红->紫）
            lut = self.create_rainbow_colormap()
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            
            return actor
            
        except Exception as e:
            print(f"Delaunay2D失败: {e}")
            return None

    def _create_surface_with_delaunay2d_range(self, ugrid: vtk.vtkUnstructuredGrid,
                                            global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """使用2D Delaunay三角化创建表面（支持全局颜色范围）"""
        try:
            # 将点转换为PolyData
            points_to_poly = vtk.vtkVertexGlyphFilter()
            points_to_poly.SetInputData(ugrid)
            points_to_poly.Update()

            # 检查点数
            poly_data = points_to_poly.GetOutput()
            if poly_data.GetNumberOfPoints() < 3:
                print("点数不足，无法进行三角化")
                return None

            # 使用简化的2D Delaunay三角化
            delaunay2d = vtk.vtkDelaunay2D()
            delaunay2d.SetInputData(poly_data)
            delaunay2d.SetProjectionPlaneMode(vtk.VTK_BEST_FITTING_PLANE)
            delaunay2d.SetAlpha(0.0)  # 不使用alpha形状
            delaunay2d.SetTolerance(0.001)

            # 设置边界条件以避免过度三角化
            bounds = ugrid.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
            delaunay2d.SetOffset(domain_size * 0.01)

            delaunay2d.Update()

            # 检查结果
            result = delaunay2d.GetOutput()
            if result.GetNumberOfCells() == 0:
                print("Delaunay2D未生成任何三角形")
                return None

            print(f"Delaunay2D生成了 {result.GetNumberOfCells()} 个三角形")

            # 创建映射器和演员
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(delaunay2d.GetOutputPort())
            mapper.SetScalarModeToUsePointData()

            # 使用全局范围或局部范围
            if global_scalar_range:
                scalar_range = global_scalar_range
                print(f"使用全局颜色范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f}")
            else:
                scalar_range = ugrid.GetPointData().GetScalars().GetRange()
                print(f"使用局部颜色范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f}")

            mapper.SetScalarRange(scalar_range)

            # 设置彩虹色颜色映射（红->紫）
            lut = self.create_rainbow_colormap()
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            return actor

        except Exception as e:
            print(f"Delaunay2D失败: {e}")
            return None






    def _create_surface_with_point_gaussian_range(self, ugrid: vtk.vtkUnstructuredGrid,
                                                global_scalar_range: Optional[Tuple[float, float]] = None) -> Optional[vtk.vtkActor]:
        """使用高斯点渲染创建表面效果（支持全局颜色范围）"""
        try:
            # 对于大数据集，进一步采样以提高性能
            working_ugrid = ugrid
            if ugrid.GetNumberOfPoints() > 20000:
                print(f"点数过多 ({ugrid.GetNumberOfPoints()})，进行额外采样...")
                working_ugrid = self.sample_ugrid_for_performance(ugrid, 20000)
                if working_ugrid is None:
                    working_ugrid = ugrid
                else:
                    print(f"采样后点数: {working_ugrid.GetNumberOfPoints()}")

            bounds = working_ugrid.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])

            # 创建球体源
            sphere = vtk.vtkSphereSource()
            sphere.SetRadius(domain_size / 500)  # 更小的球体以提高性能
            sphere.SetPhiResolution(6)  # 降低分辨率
            sphere.SetThetaResolution(6)

            # 创建字形
            glyph = vtk.vtkGlyph3D()
            glyph.SetInputData(working_ugrid)
            glyph.SetSourceConnection(sphere.GetOutputPort())
            glyph.SetScaleModeToDataScalingOff()
            glyph.SetColorModeToColorByScalar()

            # 创建映射器和演员
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())
            mapper.SetScalarModeToUsePointData()

            # 使用全局范围或局部范围
            if global_scalar_range:
                scalar_range = global_scalar_range
                print(f"使用全局颜色范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f}")
            else:
                scalar_range = working_ugrid.GetPointData().GetScalars().GetRange()
                print(f"使用局部颜色范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f}")

            mapper.SetScalarRange(scalar_range)

            # 设置彩虹色颜色映射（红->紫）
            lut = self.create_rainbow_colormap()
            lut.SetRange(scalar_range)
            mapper.SetLookupTable(lut)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetOpacity(0.7)  # 设置透明度

            print(f"高斯点渲染完成: {working_ugrid.GetNumberOfPoints()} 个点")
            return actor

        except Exception as e:
            print(f"高斯点渲染失败: {e}")
            return None

    def _create_point_cloud_visualization(self, ugrid: vtk.vtkUnstructuredGrid) -> vtk.vtkActor:
        """创建简单的点云可视化"""
        mapper = vtk.vtkDataSetMapper()
        mapper.SetInputData(ugrid)

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetPointSize(3)
        actor.GetProperty().SetColor(0.8, 0.8, 0.8)  # 灰色点

        return actor
