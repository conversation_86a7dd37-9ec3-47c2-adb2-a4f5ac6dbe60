#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制面板
负责创建和管理用户界面控制组件
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    QT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import *
        from PyQt6.QtCore import *
        from PyQt6.QtGui import *
        QT_VERSION = 6
    except ImportError:
        print("错误: 未安装PyQt5或PyQt6")
        raise

from typing import Dict, List, Callable, Optional
import os


class ControlPanel(QWidget):
    """控制面板类"""
    
    # 信号定义
    load_mesh_requested = pyqtSignal()
    load_steady_requested = pyqtSignal()
    load_transient_requested = pyqtSignal()
    create_visualization_requested = pyqtSignal()
    update_display_requested = pyqtSignal()
    render_mesh_only_requested = pyqtSignal()
    save_image_requested = pyqtSignal()
    export_vtk_requested = pyqtSignal()
    display_options_changed = pyqtSignal()
    region_selection_changed = pyqtSignal()
    visualization_parameters_changed = pyqtSignal()
    opacity_changed = pyqtSignal()
    create_3d_streamlines_clicked = pyqtSignal()  # 新增3D流线信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.region_checkboxes: Dict[str, QCheckBox] = {}
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setFixedWidth(300)
        layout = QVBoxLayout(self)
        
        # 数据加载组
        data_group = self.create_data_loading_group()
        layout.addWidget(data_group)
        
        # 可视化控制组
        viz_group = self.create_visualization_control_group()
        layout.addWidget(viz_group)
        
        # 区域选择组
        self.region_group = self.create_region_selection_group()
        layout.addWidget(self.region_group)
        
        # 导出功能组
        export_group = self.create_export_group()
        layout.addWidget(export_group)
        
        # 添加弹性空间
        layout.addStretch()
        
    def create_data_loading_group(self) -> QGroupBox:
        """创建数据加载组"""
        group = QGroupBox("数据加载")
        layout = QVBoxLayout(group)
        
        # self.load_mesh_btn = QPushButton("加载网格数据")
        # self.load_mesh_btn.clicked.connect(self.load_mesh_requested.emit)
        # layout.addWidget(self.load_mesh_btn)
        
        self.load_steady_btn = QPushButton("载入数据")
        self.load_steady_btn.clicked.connect(self.load_steady_requested.emit)
        layout.addWidget(self.load_steady_btn)
        
        # self.load_transient_btn = QPushButton("加载瞬态结果")
        # self.load_transient_btn.clicked.connect(self.load_transient_requested.emit)
        # layout.addWidget(self.load_transient_btn)
        
        return group
        
    def create_visualization_control_group(self) -> QGroupBox:
        """创建可视化控制组"""
        group = QGroupBox("可视化控制")
        layout = QVBoxLayout(group)
        
        # 数据源选择
        source_layout = QHBoxLayout()
        source_layout.addWidget(QLabel("数据源:"))
        self.data_source_group = QButtonGroup()
        self.steady_radio = QRadioButton("稳态")
        self.transient_radio = QRadioButton("瞬态")
        self.steady_radio.setChecked(True)
        self.data_source_group.addButton(self.steady_radio, 0)
        self.data_source_group.addButton(self.transient_radio, 1)
        source_layout.addWidget(self.steady_radio)
        source_layout.addWidget(self.transient_radio)
        layout.addLayout(source_layout)
        
        # 显示选项
        self.show_pressure_cb = QCheckBox("压力分布")
        self.show_pressure_cb.setChecked(True)
        self.show_pressure_cb.stateChanged.connect(self.display_options_changed.emit)
        self.show_pressure_cb.stateChanged.connect(self.update_display_requested.emit)
        layout.addWidget(self.show_pressure_cb)

        self.show_pressure_contours_cb = QCheckBox("壁面压力轮廓")
        self.show_pressure_contours_cb.setChecked(False)
        self.show_pressure_contours_cb.stateChanged.connect(self.display_options_changed.emit)
        layout.addWidget(self.show_pressure_contours_cb)

        self.show_streamlines_cb = QCheckBox("流线")
        self.show_streamlines_cb.setChecked(True)
        self.show_streamlines_cb.stateChanged.connect(self.display_options_changed.emit)
        self.show_streamlines_cb.stateChanged.connect(self.update_display_requested.emit)
        layout.addWidget(self.show_streamlines_cb)

        self.show_vectors_cb = QCheckBox("速度矢量")
        self.show_vectors_cb.stateChanged.connect(self.display_options_changed.emit)
        self.show_vectors_cb.stateChanged.connect(self.update_display_requested.emit)
        layout.addWidget(self.show_vectors_cb)

        self.show_mesh_cb = QCheckBox("网格面片")
        self.show_mesh_cb.stateChanged.connect(self.display_options_changed.emit)
        layout.addWidget(self.show_mesh_cb)

        # 仅渲染网格按钮
        self.render_mesh_only_btn = QPushButton("仅渲染网格")
        self.render_mesh_only_btn.clicked.connect(self.render_mesh_only_requested.emit)
        layout.addWidget(self.render_mesh_only_btn)
        
        # 参数控制
        params_layout = self.create_parameters_layout()
        layout.addLayout(params_layout)
        
        # 操作按钮
        self.create_viz_btn = QPushButton("生成可视化")
        self.create_viz_btn.clicked.connect(self.create_visualization_requested.emit)
        layout.addWidget(self.create_viz_btn)
        
        self.update_viz_btn = QPushButton("更新显示")
        self.update_viz_btn.clicked.connect(self.update_display_requested.emit)
        layout.addWidget(self.update_viz_btn)
        
        return group
        
    def create_parameters_layout(self) -> QFormLayout:
        """创建参数控制布局"""
        params_layout = QFormLayout()
        
        # 流线密度
        self.streamline_density = QSlider(Qt.Horizontal)
        self.streamline_density.setRange(1, 100)
        self.streamline_density.setValue(50)
        self.streamline_density_label = QLabel("50")
        self.streamline_density.valueChanged.connect(
            lambda v: self.streamline_density_label.setText(str(v)))
        # self.streamline_density.valueChanged.connect(self.visualization_parameters_changed.emit)
        density_layout = QHBoxLayout()
        density_layout.addWidget(self.streamline_density)
        density_layout.addWidget(self.streamline_density_label)
        params_layout.addRow("流线密度:", density_layout)
        
        # 矢量缩放
        self.vector_scale = QSlider(Qt.Horizontal)
        self.vector_scale.setRange(1, 20)
        self.vector_scale.setValue(10)
        self.vector_scale_label = QLabel("1.0")
        self.vector_scale.valueChanged.connect(
            lambda v: self.vector_scale_label.setText(f"{v/10:.1f}"))
        self.vector_scale.valueChanged.connect(self.visualization_parameters_changed.emit)
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(self.vector_scale)
        scale_layout.addWidget(self.vector_scale_label)
        params_layout.addRow("矢量缩放:", scale_layout)
        
        # 数据采样
        self.data_sampling = QSlider(Qt.Horizontal)
        self.data_sampling.setRange(1, 10)
        self.data_sampling.setValue(1)
        self.data_sampling_label = QLabel("1")
        self.data_sampling.valueChanged.connect(
            lambda v: self.data_sampling_label.setText(str(v)))
        self.data_sampling.valueChanged.connect(self.visualization_parameters_changed.emit)
        sampling_layout = QHBoxLayout()
        sampling_layout.addWidget(self.data_sampling)
        sampling_layout.addWidget(self.data_sampling_label)
        params_layout.addRow("数据采样:", sampling_layout)

        # 壁面轮廓参数
        self.contour_count = QSlider(Qt.Horizontal)
        self.contour_count.setRange(5, 30)
        self.contour_count.setValue(15)
        self.contour_count_label = QLabel("15")
        self.contour_count.valueChanged.connect(
            lambda v: self.contour_count_label.setText(str(v)))
        self.contour_count.valueChanged.connect(self.visualization_parameters_changed.emit)
        contour_count_layout = QHBoxLayout()
        contour_count_layout.addWidget(self.contour_count)
        contour_count_layout.addWidget(self.contour_count_label)
        params_layout.addRow("轮廓线数量:", contour_count_layout)

        self.contour_line_width = QSlider(Qt.Horizontal)
        self.contour_line_width.setRange(1, 5)
        self.contour_line_width.setValue(2)
        self.contour_line_width_label = QLabel("2")
        self.contour_line_width.valueChanged.connect(
            lambda v: self.contour_line_width_label.setText(str(v)))
        self.contour_line_width.valueChanged.connect(self.visualization_parameters_changed.emit)
        contour_line_width_layout = QHBoxLayout()
        contour_line_width_layout.addWidget(self.contour_line_width)
        contour_line_width_layout.addWidget(self.contour_line_width_label)
        params_layout.addRow("轮廓线宽度:", contour_line_width_layout)

        # 透明度控制
        self.pressure_opacity = QSlider(Qt.Horizontal)
        self.pressure_opacity.setRange(0, 100)
        self.pressure_opacity.setValue(80)
        self.pressure_opacity_label = QLabel("0.8")
        self.pressure_opacity.valueChanged.connect(
            lambda v: self.pressure_opacity_label.setText(f"{v/100:.1f}"))
        self.pressure_opacity.valueChanged.connect(self.opacity_changed.emit)
        pressure_opacity_layout = QHBoxLayout()
        pressure_opacity_layout.addWidget(self.pressure_opacity)
        pressure_opacity_layout.addWidget(self.pressure_opacity_label)
        params_layout.addRow("压力透明度:", pressure_opacity_layout)

        self.mesh_opacity = QSlider(Qt.Horizontal)
        self.mesh_opacity.setRange(0, 100)
        self.mesh_opacity.setValue(60)
        self.mesh_opacity_label = QLabel("0.6")
        self.mesh_opacity.valueChanged.connect(
            lambda v: self.mesh_opacity_label.setText(f"{v/100:.1f}"))
        self.mesh_opacity.valueChanged.connect(self.opacity_changed.emit)
        mesh_opacity_layout = QHBoxLayout()
        mesh_opacity_layout.addWidget(self.mesh_opacity)
        mesh_opacity_layout.addWidget(self.mesh_opacity_label)
        params_layout.addRow("网格透明度:", mesh_opacity_layout)

        self.surface_opacity = QSlider(Qt.Horizontal)
        self.surface_opacity.setRange(0, 100)
        self.surface_opacity.setValue(90)
        self.surface_opacity_label = QLabel("0.9")
        self.surface_opacity.valueChanged.connect(
            lambda v: self.surface_opacity_label.setText(f"{v/100:.1f}"))
        self.surface_opacity.valueChanged.connect(self.opacity_changed.emit)
        surface_opacity_layout = QHBoxLayout()
        surface_opacity_layout.addWidget(self.surface_opacity)
        surface_opacity_layout.addWidget(self.surface_opacity_label)
        params_layout.addRow("面单元透明度:", surface_opacity_layout)

        return params_layout
        
    def create_region_selection_group(self) -> QGroupBox:
        """创建区域选择组"""
        group = QGroupBox("区域选择")
        layout = QVBoxLayout(group)

        # 全选/全不选按钮
        region_buttons_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_regions)
        self.select_none_btn = QPushButton("全不选")
        self.select_none_btn.clicked.connect(self.select_none_regions)
        region_buttons_layout.addWidget(self.select_all_btn)
        region_buttons_layout.addWidget(self.select_none_btn)
        layout.addLayout(region_buttons_layout)

        # 区域复选框容器（滚动区域）
        self.region_scroll = QScrollArea()
        self.region_scroll.setWidgetResizable(True)
        self.region_scroll.setMinimumHeight(200)
        self.region_scroll.setMaximumHeight(300)
        self.region_widget = QWidget()
        self.region_checkboxes_layout = QVBoxLayout(self.region_widget)
        self.region_scroll.setWidget(self.region_widget)
        layout.addWidget(self.region_scroll)

        # 初始化时隐藏区域选择组
        group.setVisible(False)
        
        return group
        
    def create_export_group(self) -> QGroupBox:
        """创建导出功能组"""
        group = QGroupBox("导出功能")
        layout = QVBoxLayout(group)
        
        self.save_image_btn = QPushButton("保存图像")
        self.save_image_btn.clicked.connect(self.save_image_requested.emit)
        layout.addWidget(self.save_image_btn)
        
        self.export_vtk_btn = QPushButton("导出VTK")
        self.export_vtk_btn.clicked.connect(self.export_vtk_requested.emit)
        layout.addWidget(self.export_vtk_btn)
        
        return group
        
    def update_region_controls(self, region_names: List[str]):
        """更新区域选择控件"""
        # 清除现有复选框
        for checkbox in self.region_checkboxes.values():
            checkbox.setParent(None)
        self.region_checkboxes.clear()

        # 如果有区域数据，显示区域选择组
        if region_names:
            self.region_group.setVisible(True)

            # 创建复选框
            for region_name in sorted(region_names):
                checkbox = QCheckBox(region_name)
                checkbox.setChecked(True)  # 默认选中
                # 连接信号，当复选框状态改变时发出信号
                checkbox.stateChanged.connect(self.region_selection_changed.emit)
                self.region_checkboxes[region_name] = checkbox
                self.region_checkboxes_layout.addWidget(checkbox)

            print(f"更新区域控件: {len(region_names)} 个区域 - {region_names}")
        else:
            self.region_group.setVisible(False)
            print("没有找到区域数据，隐藏区域选择控件")
            
    def select_all_regions(self):
        """选中所有区域"""
        for checkbox in self.region_checkboxes.values():
            checkbox.setChecked(True)

    def select_none_regions(self):
        """取消选中所有区域"""
        for checkbox in self.region_checkboxes.values():
            checkbox.setChecked(False)
            
    def get_selected_regions(self) -> List[str]:
        """获取选中的区域列表"""
        selected = []
        for region_name, checkbox in self.region_checkboxes.items():
            if checkbox.isChecked():
                selected.append(region_name)
        return selected
        
    def get_data_source(self) -> str:
        """获取当前选择的数据源"""
        return "steady" if self.steady_radio.isChecked() else "transient"
        
    def get_display_options(self) -> Dict[str, bool]:
        """获取显示选项"""
        return {
            'show_pressure': self.show_pressure_cb.isChecked(),
            'show_contours': self.show_pressure_contours_cb.isChecked(),
            'show_streamlines': self.show_streamlines_cb.isChecked(),
            'show_vectors': self.show_vectors_cb.isChecked(),
            'show_mesh': self.show_mesh_cb.isChecked(),
        }
        
    def get_visualization_parameters(self) -> Dict[str, float]:
        """获取可视化参数"""
        return {
            'streamline_density': self.streamline_density.value(),
            'vector_scale': self.vector_scale.value() / 10.0,
            'sampling_rate': self.data_sampling.value(),
            'contour_count': self.contour_count.value(),
            'contour_line_width': self.contour_line_width.value()
        }

    def get_opacity_parameters(self) -> Dict[str, float]:
        """获取透明度参数"""
        return {
            'pressure_opacity': self.pressure_opacity.value() / 100.0,
            'mesh_opacity': self.mesh_opacity.value() / 100.0,
            'surface_opacity': self.surface_opacity.value() / 100.0
        }
